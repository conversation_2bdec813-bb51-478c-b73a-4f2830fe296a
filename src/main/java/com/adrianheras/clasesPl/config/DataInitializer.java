package com.adrianheras.clasesPl.config;

import com.adrianheras.clasesPl.model.Course;
import com.adrianheras.clasesPl.model.User;
import com.adrianheras.clasesPl.repository.CourseRepository;
import com.adrianheras.clasesPl.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Componente para inicializar datos por defecto en la base de datos.
 * Se ejecuta automáticamente después de que Spring Boot inicie y las tablas estén creadas.
 */
@Component
public class DataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializer.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private CourseRepository courseRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        logger.info("🚀 INICIANDO INICIALIZACIÓN DE DATOS - ClasesPL");
        logger.info("📅 Fecha y hora: {}", LocalDateTime.now());
        
        try {
            initializeUsers();
            initializeCourses();
            
            logger.info("🎉 INICIALIZACIÓN DE DATOS COMPLETADA EXITOSAMENTE");
            logger.info("📊 Resumen:");
            logger.info("   - Usuarios totales: {}", userRepository.count());
            logger.info("   - Cursos totales: {}", courseRepository.count());
            logger.info("========================================");
            
        } catch (Exception e) {
            logger.error("❌ ERROR durante la inicialización de datos: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * Inicializa usuarios por defecto si no existen
     */
    private void initializeUsers() {
        logger.info("👥 Verificando usuarios por defecto...");

        // Crear usuario profesor
        if (!userRepository.existsByUsername("profe")) {
            logger.info("👤 Creando usuario 1...");
            
            User profe = new User();
            profe.setUsername("profe");
            profe.setEmail("<EMAIL>");
            profe.setPassword(passwordEncoder.encode("profe123"));
            profe.setFirstName("Administrador");
            profe.setLastName("ClasesPL");
            profe.setRole(User.Role.PROFESOR);
            profe.setEnabled(true);
            profe.setCreatedAt(LocalDateTime.now());
            
            userRepository.save(profe);
            logger.info("✅ Usuario 1 creado");
        } else {
            logger.info("ℹ️ Usuario 1 ya existe, omitiendo creación");
        }

        // Crear usuario estudiante de ejemplo
        if (!userRepository.existsByUsername("estudiante1")) {
            logger.info("🎓 Creando usuario 2 ...");
            
            User student = new User();
            student.setUsername("estudiante1");
            student.setEmail("<EMAIL>");
            student.setPassword(passwordEncoder.encode("student123"));
            student.setFirstName("Jan");
            student.setLastName("Kowalski");
            student.setRole(User.Role.ALUMNO);
            student.setEnabled(true);
            student.setCreatedAt(LocalDateTime.now());
            
            userRepository.save(student);
            logger.info("✅ Usuario 2");
        } else {
            logger.info("ℹ️ Usuario 2, omitiendo creación");
        }
    }

    /**
     * Inicializa cursos por defecto si no existen
     */
    private void initializeCourses() {
        logger.info("📚 Verificando cursos por defecto...");

        // Buscar el usuario profe para asignar como creador del curso
        User profe = userRepository.findByUsername("profe").orElse(null);
        
        if (profe == null) {
            logger.warn("⚠️ No se encontró usuario 1, omitiendo creación de cursos");
            return;
        }

        // Crear curso de ejemplo
        String courseTitle = "Español Básico - Nivel A1";
        if (!courseRepository.existsByTitle(courseTitle)) {
            logger.info("📖 Creando curso de ejemplo...");
            
            Course course = new Course();
            course.setTitle(courseTitle);
            course.setCreatedBy(profe);
            course.setCreatedAt(LocalDateTime.now());
            
            courseRepository.save(course);
            logger.info("✅ Curso creado: {}", courseTitle);
        } else {
            logger.info("ℹ️ Curso '{}' ya existe, omitiendo creación", courseTitle);
        }
    }
}
